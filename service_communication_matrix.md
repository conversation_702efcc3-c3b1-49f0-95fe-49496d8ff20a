# Service Communication Matrix

## Overview
This document defines the exact service-to-service communication patterns, which services can call which methods, and the authentication requirements.

## Auth Service RPC Methods

### 🔓 **Public Methods** (No Service Authentication Required)
These methods can be called by any service without client-id/client-key:

| Method | Description | Called By |
|--------|-------------|-----------|
| `Login` | User authentication | API Gateway |
| `HealthCheck` | Service health status | Load balancers, monitoring |

### 🔐 **Protected Methods** (Require client-id/client-key)
These methods require valid service credentials:

| Method | Description | Allowed Services |
|--------|-------------|------------------|
| `ValidateUserToken` | Validate JWT tokens | coupon-service, order-service |
| `RevokeToken` | Revoke user tokens | coupon-service |
| `CreateUserCredentialsWithRole` | Create user credentials with role | user-service |
| `GetUserRoles` | Get user roles | user-service |
| `UpdateUserEmail` | Update user email | user-service |

### 🔧 **Service Management Methods** (Admin Only)
These methods require admin service credentials:

| Method | Description | Usage |
|--------|-------------|-------|
| `RegisterService` | Register new service | Service deployment/setup |
| `ValidateServiceCredentials` | Validate service credentials | Internal validation |

## Service Communication Patterns

### 1. **API Gateway → Auth Service**
```
API Gateway --[Login]--> Auth Service
```
- **Methods**: `Login`
- **Authentication**: None (public method)
- **Purpose**: User authentication for web/mobile clients

### 2. **User Service → Auth Service**
```
User Service --[CreateUserCredentialsWithRole, GetUserRoles, UpdateUserEmail]--> Auth Service
```
- **Methods**: `CreateUserCredentialsWithRole`, `GetUserRoles`, `UpdateUserEmail`
- **Authentication**: client-id/client-key (user-service credentials)
- **Purpose**: User management and role synchronization

### 3. **Coupon Service → Auth Service**
```
Coupon Service --[ValidateUserToken, RevokeToken]--> Auth Service
```
- **Methods**: `ValidateUserToken`, `RevokeToken`
- **Authentication**: client-id/client-key (coupon-service credentials)
- **Purpose**: Token validation and revocation

### 4. **Order Service → Auth Service**
```
Order Service --[ValidateUserToken]--> Auth Service
```
- **Methods**: `ValidateUserToken`
- **Authentication**: client-id/client-key (order-service credentials)
- **Purpose**: Token validation for order operations

## Service Credentials Setup

### Required Service Registrations
Each service must be registered with auth-service to obtain credentials:

```bash
# Register user-service
curl -X POST http://auth-service:8080/admin/services \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "service_name": "user-service",
    "service_version": "1.0.0",
    "required_permissions": ["user_management"]
  }'

# Register coupon-service
curl -X POST http://auth-service:8080/admin/services \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "service_name": "coupon-service", 
    "service_version": "1.0.0",
    "required_permissions": ["token_validation", "token_revocation"]
  }'

# Register order-service
curl -X POST http://auth-service:8080/admin/services \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "service_name": "order-service",
    "service_version": "1.0.0", 
    "required_permissions": ["token_validation"]
  }'
```

### Configuration Example
Each service must configure its credentials:

```yaml
# user-service config
service:
  name: "user-service"
  client_id: "${USER_SERVICE_CLIENT_ID}"
  client_key: "${USER_SERVICE_CLIENT_KEY}"

downstream_services:
  auth_service_addr: "auth-service:50051"
```

## Security Enforcement

### 1. **Method Authorization Matrix**
The auth-service enforces this matrix in code:

```go
var methodAllowList = map[string][]string{
    "coupon-service": {"ValidateUserToken", "RevokeToken"},
    "order-service":  {"ValidateUserToken"},
    "user-service":   {"CreateUserCredentialsWithRole", "GetUserRoles", "UpdateUserEmail"},
}
```

### 2. **Authentication Flow**
1. Service makes gRPC call with metadata:
   ```
   client-id: <service-client-id>
   client-key: <service-client-key>
   ```
2. Auth-service validates credentials against database
3. Auth-service checks method allowlist for the service
4. If authorized, method executes; otherwise returns 403 Forbidden

### 3. **Error Responses**
- **401 Unauthorized**: Missing or invalid credentials
- **403 Forbidden**: Valid credentials but method not allowed for service
- **404 Not Found**: Service not registered

## Implementation Status

### ✅ **Completed**
- [x] Removed unused `CreateUserCredentials` method
- [x] Added proper authorization to all protected methods
- [x] Implemented service allowlist enforcement
- [x] Updated proto definitions with clear method categories
- [x] User-service properly uses client-id/client-key

### 🔄 **Next Steps**
- [ ] Register coupon-service and order-service (if they exist)
- [ ] Add admin endpoint for service registration
- [ ] Implement service credential rotation
- [ ] Add monitoring for unauthorized access attempts

## Testing Service Communication

### Test Valid Service Call
```bash
# From user-service to auth-service
grpcurl -H "client-id: user-service-client-id" \
        -H "client-key: user-service-client-key" \
        -d '{"user_id": "123"}' \
        auth-service:50051 auth.v1.AuthService/GetUserRoles
```

### Test Unauthorized Call
```bash
# Should fail - coupon-service trying to call user-service method
grpcurl -H "client-id: coupon-service-client-id" \
        -H "client-key: coupon-service-client-key" \
        -d '{"user_id": "123"}' \
        auth-service:50051 auth.v1.AuthService/GetUserRoles
```

This matrix ensures clear service boundaries and proper authentication for all inter-service communication.
