package middleware

import (
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
)

func JWTAuth(jwtManager *auth.JWTManager) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			authHeader := c.Request().Header.Get("Authorization")
			if !strings.HasPrefix(authHeader, "Bearer ") {
				return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("missing or malformed JWT"))
			}
			tokenString := authHeader[7:]

			claims, err := jwtManager.ValidateToken(tokenString)
			if err != nil {
				return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid or expired JWT"))
			}

			c.Set("userID", claims.UserID)
			c.Set("userEmail", claims.Email)
			c.Set("userRoles", claims.Roles)

			return next(c)
		}
	}
}
