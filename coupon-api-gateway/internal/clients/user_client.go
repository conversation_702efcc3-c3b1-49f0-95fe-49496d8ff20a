package clients

import (
	"context"
	"fmt"

	user_proto_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type UserClient struct {
	Client user_proto_v1.UserServiceClient
	conn   *shared_grpc.Client
}

func NewUserClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics) (*UserClient, error) {
	client, err := shared_grpc.NewClient(target, cfg, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for user service: %w", err)
	}
	return &UserClient{
		Client: user_proto_v1.NewUserServiceClient(client.GetConnection()),
		conn:   client,
	}, nil
}

func (c *UserClient) Close() {
	c.conn.Close()
}

func (c *UserClient) CreateUser(ctx context.Context, name, email, password string) (*user_proto_v1.CreateUserResponse, error) {
	req := &user_proto_v1.CreateUserRequest{
		Name:     name,
		Email:    email,
		Password: password,
	}
	return c.Client.CreateUser(ctx, req)
}

func (c *UserClient) GetUser(ctx context.Context, userID string) (*user_proto_v1.GetUserResponse, error) {
	req := &user_proto_v1.GetUserRequest{UserId: userID}
	return c.Client.GetUser(ctx, req)
}
