package clients

import (
	"context"
	"fmt"

	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

type AuthClient struct {
	Client proto_auth_v1.AuthServiceClient
	conn   *shared_grpc.Client
}

func NewAuthClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics) (*AuthClient, error) {
	client, err := shared_grpc.NewClient(target, cfg, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for auth service: %w", err)
	}

	return &AuthClient{
		Client: proto_auth_v1.NewAuthServiceClient(client.GetConnection()),
		conn:   client,
	}, nil
}

func (c *AuthClient) Close() {
	c.conn.Close()
}

func (c *AuthClient) Login(ctx context.Context, email, password string) (*proto_auth_v1.LoginResponse, error) {
	req := &proto_auth_v1.LoginRequest{
		Email:    email,
		Password: password,
	}
	return c.Client.Login(ctx, req)
}
