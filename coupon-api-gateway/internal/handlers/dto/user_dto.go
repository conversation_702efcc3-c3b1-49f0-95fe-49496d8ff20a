package dto

import (
	"time"

	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
)

type UserResponse struct {
	ID        string    `json:"id"`
	Email     string    `json:"email"`
	Name      string    `json:"name"`
	Roles     []string  `json:"roles"`
	CreatedAt time.Time `json:"created_at"`
}

func ToUserResponse(user *proto_user_v1.User) *UserResponse {
	if user == nil {
		return nil
	}
	return &UserResponse{
		ID:        user.Id,
		Email:     user.Email,
		Name:      user.Name,
		Roles:     user.Roles,
		CreatedAt: user.CreatedAt.AsTime(),
	}
}
