package handlers

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers/dto"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type AuthHandler struct {
	userClient *clients.UserClient
	authClient *clients.AuthClient
	logger     *logging.Logger
}

func NewAuthHandler(u *clients.UserClient, a *clients.AuthClient, l *logging.Logger) *AuthHandler {
	return &AuthHandler{userClient: u, authClient: a, logger: l}
}

func (h *AuthHandler) RegisterPublicRoutes(g *echo.Group) {
	g.POST("/register", h.HandleRegister)
	g.POST("/login", h.<PERSON><PERSON>og<PERSON>)
}

// HandleRegister godoc
// @Summary Register a new user
// @Description Creates a new user profile and credentials, and returns auth tokens.
// @Tags Auth
// @Accept  json
// @Produce  json
// @Param   body  body   dto.RegisterRequest  true  "Registration Information"
// @Success 201 {object} dto.RegisterResponse "User profile and auth tokens"
// @Failure 400 {object} utils.ErrorResponse "Invalid request body or validation error"
// @Failure 409 {object} utils.ErrorResponse "User with this email already exists"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /register [post]
func (h *AuthHandler) HandleRegister(c echo.Context) error {
	var req dto.RegisterRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid request body"))
	}
	if err := c.Validate(req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse(err.Error()))
	}

	ctx := c.Request().Context()

	createUserRes, err := h.userClient.CreateUser(ctx, req.Name, req.Email, req.Password)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	loginRes, err := h.authClient.Login(ctx, req.Email, req.Password)
	if err != nil {
		h.logger.WithContext(ctx).Errorf("Login failed immediately after registration for email %s: %v", req.Email, err)
		return utils.HandleGRPCError(c, err, h.logger)
	}

	response := &dto.RegisterResponse{
		User:         dto.ToUserResponse(createUserRes.User),
		AccessToken:  loginRes.AccessToken,
		RefreshToken: loginRes.RefreshToken,
	}

	return c.JSON(http.StatusCreated, response)
}

// HandleLogin godoc
// @Summary Log in a user
// @Description Authenticates a user and returns a JWT.
// @Tags Auth
// @Accept  json
// @Produce  json
// @Param   body  body   dto.LoginRequest  true  "Login Credentials"
// @Success 200 {object} dto.LoginResponse
// @Failure 400 {object} utils.ErrorResponse "Invalid request body"
// @Failure 401 {object} utils.ErrorResponse "Invalid credentials"
// @Failure 500 {object} utils.ErrorResponse "Internal server error"
// @Router /login [post]
func (h *AuthHandler) HandleLogin(c echo.Context) error {
	var req dto.LoginRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("invalid request body"))
	}
	if err := c.Validate(req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse(err.Error()))
	}

	res, err := h.authClient.Login(c.Request().Context(), req.Email, req.Password)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	return c.JSON(http.StatusOK, dto.LoginResponse{
		AccessToken:  res.AccessToken,
		RefreshToken: res.RefreshToken,
	})
}
