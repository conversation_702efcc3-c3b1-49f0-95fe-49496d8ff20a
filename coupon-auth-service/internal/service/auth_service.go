package service

import (
	"context"
	"fmt"
	"slices"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/repository"
	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"golang.org/x/crypto/bcrypt"
	"google.golang.org/grpc/metadata"
)

type AuthService interface {
	Login(ctx context.Context, req *proto_auth_v1.LoginRequest) (*proto_auth_v1.LoginResponse, error)
	CreateUserCredentialsWithRole(ctx context.Context, userID, email, password, roleName string) error
	GetUserRoles(ctx context.Context, userID string) ([]string, error)
	UpdateUserEmail(ctx context.Context, userID, newEmail string) error
	RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error)
	ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error)
	ValidateUserToken(ctx context.Context, req *proto_auth_v1.ValidateUserTokenRequest) (*proto_auth_v1.ValidateUserTokenResponse, error)
	RevokeToken(ctx context.Context, req *proto_auth_v1.RevokeTokenRequest) (*proto_auth_v1.RevokeTokenResponse, error)
}

type authService struct {
	repo       repository.AuthRepository
	jwtManager *auth.JWTManager
	logger     *logging.Logger
	cfg        *config.Config
}

// Service-to-service method authorization matrix
var methodAllowList = map[string][]string{
	"coupon-service": {"ValidateUserToken", "RevokeToken"},
	"order-service":  {"ValidateUserToken"},
	"user-service":   {"CreateUserCredentialsWithRole", "GetUserRoles", "UpdateUserEmail"},
	"api-gateway":    {}, // API Gateway only calls public methods (Login)
}

// Public methods that don't require service authentication
var publicMethods = map[string]bool{
	"Login":       true,
	"HealthCheck": true,
}

func (s *authService) authorizeService(ctx context.Context, method string) error {
	// Check if method is public (no authorization required)
	if publicMethods[method] {
		return nil
	}

	// For non-public methods, require service authentication
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return errors.NewUnauthorizedError("missing service credentials")
	}
	ids := md.Get("client-id")
	keys := md.Get("client-key")
	if len(ids) == 0 || len(keys) == 0 {
		return errors.NewUnauthorizedError("missing service credentials")
	}
	cred, err := s.repo.GetServiceByClientID(ctx, ids[0])
	if err != nil {
		return errors.NewUnauthorizedError("invalid client id or key")
	}
	if bcrypt.CompareHashAndPassword([]byte(cred.ClientKey), []byte(keys[0])) != nil {
		return errors.NewUnauthorizedError("invalid client id or key")
	}
	allowed, ok := methodAllowList[cred.Name]
	if !ok || !slices.Contains(allowed, method) {
		return errors.NewForbiddenError(fmt.Sprintf("method %s not allowed for service %s", method, cred.Name))
	}
	return nil
}

func NewAuthService(repo repository.AuthRepository, jwtManager *auth.JWTManager, logger *logging.Logger, cfg *config.Config) AuthService {
	return &authService{
		repo:       repo,
		jwtManager: jwtManager,
		logger:     logger,
		cfg:        cfg,
	}
}

func (s *authService) Login(ctx context.Context, req *proto_auth_v1.LoginRequest) (*proto_auth_v1.LoginResponse, error) {
	// Login is a public method - no service authorization required
	if err := s.authorizeService(ctx, "Login"); err != nil {
		return nil, err
	}

	cred, err := s.repo.GetUserCredentialByEmail(ctx, req.Email)
	if err != nil {
		return nil, errors.NewUnauthorizedError("invalid credentials")
	}
	if bcrypt.CompareHashAndPassword([]byte(cred.PasswordHash), []byte(req.Password)) != nil {
		return nil, errors.NewUnauthorizedError("invalid credentials")
	}

	roles, err := s.repo.GetUserRoles(ctx, cred.UserID)
	if err != nil {
		return nil, errors.NewInternalError("could not fetch user roles")
	}

	token, err := s.jwtManager.GenerateToken(cred.UserID, cred.Email, roles)
	if err != nil {
		return nil, errors.NewInternalError("failed to generate token")
	}

	return &proto_auth_v1.LoginResponse{
		AccessToken:  token,
		RefreshToken: "",
	}, nil
}

func (s *authService) CreateUserCredentialsWithRole(ctx context.Context, userID, email, password, roleName string) error {
	if err := s.authorizeService(ctx, "CreateUserCredentialsWithRole"); err != nil {
		return err
	}

	log := s.logger.WithContext(ctx)
	log.Infof("Attempting to create credentials with role %s for user_id: %s", roleName, userID)

	// Create credentials
	cred, err := model.NewUserCredential(userID, email, password)
	if err != nil {
		return errors.NewInternalError(fmt.Sprintf("failed to prepare credentials: %v", err))
	}

	if err := s.repo.CreateUserCredential(ctx, cred); err != nil {
		return errors.NewInternalError(fmt.Sprintf("failed to store credentials: %v", err))
	}

	// Assign role
	if err := s.repo.AssignUserRole(ctx, userID, roleName); err != nil {
		log.Errorf("Failed to assign role %s to user %s: %v", roleName, userID, err)
		return errors.NewInternalError(fmt.Sprintf("failed to assign role: %v", err))
	}

	log.Infof("Successfully created credentials and assigned role %s for user_id: %s", roleName, userID)
	return nil
}

func (s *authService) GetUserRoles(ctx context.Context, userID string) ([]string, error) {
	if err := s.authorizeService(ctx, "GetUserRoles"); err != nil {
		return nil, err
	}
	return s.repo.GetUserRoles(ctx, userID)
}

func (s *authService) UpdateUserEmail(ctx context.Context, userID, newEmail string) error {
	if err := s.authorizeService(ctx, "UpdateUserEmail"); err != nil {
		return err
	}

	log := s.logger.WithContext(ctx)
	log.Infof("Updating email for user_id: %s", userID)

	if err := s.repo.UpdateUserEmail(ctx, userID, newEmail); err != nil {
		return errors.NewInternalError(fmt.Sprintf("failed to update email: %v", err))
	}

	log.Infof("Successfully updated email for user_id: %s", userID)
	return nil
}

func (s *authService) ValidateUserToken(ctx context.Context, req *proto_auth_v1.ValidateUserTokenRequest) (*proto_auth_v1.ValidateUserTokenResponse, error) {
	if err := s.authorizeService(ctx, "ValidateUserToken"); err != nil {
		return nil, err
	}
	claims, err := s.jwtManager.ValidateToken(req.Token)
	if err != nil {
		return nil, errors.NewUnauthorizedError("invalid token")
	}

	isRevoked, err := s.repo.IsTokenRevoked(ctx, claims.ID)
	if err != nil {
		return nil, errors.NewInternalError("could not verify token status")
	}
	if isRevoked {
		return nil, errors.NewUnauthorizedError("token has been revoked")
	}

	if req.RequiredRole != "" {
		hasRole := slices.Contains(claims.Roles, req.RequiredRole)
		if !hasRole {
			return nil, errors.NewForbiddenError("user does not have the required role")
		}
	}

	return &proto_auth_v1.ValidateUserTokenResponse{
		Valid:  true,
		UserId: claims.UserID,
		Email:  claims.Email,
		Roles:  claims.Roles,
	}, nil
}

func (s *authService) RevokeToken(ctx context.Context, req *proto_auth_v1.RevokeTokenRequest) (*proto_auth_v1.RevokeTokenResponse, error) {
	if err := s.authorizeService(ctx, "RevokeToken"); err != nil {
		return nil, err
	}
	claims, err := s.jwtManager.ValidateToken(req.Token)
	if err != nil {
		return nil, errors.NewValidationError("invalid token to revoke", nil)
	}

	if err := s.repo.RevokeToken(ctx, claims.ID); err != nil {
		return nil, errors.NewInternalError("failed to revoke token")
	}

	return &proto_auth_v1.RevokeTokenResponse{Revoked: true}, nil
}

func (s *authService) RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error) {
	if err := s.authorizeService(ctx, "RegisterService"); err != nil {
		return nil, err
	}
	cred, rawKey, err := model.NewServiceCredential(req.ServiceName, req.ServiceVersion)
	if err != nil {
		return nil, errors.NewInternalError("could not generate credentials")
	}

	if err := s.repo.RegisterService(ctx, cred); err != nil {
		return nil, errors.NewInternalError("failed to save service credentials")
	}

	return &proto_auth_v1.RegisterServiceResponse{
		ServiceId: cred.ID,
		ClientId:  cred.ClientID,
		ClientKey: rawKey,
	}, nil
}

func (s *authService) ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error) {
	cred, err := s.repo.GetServiceByClientID(ctx, req.ClientId)
	if err != nil {
		return nil, errors.NewUnauthorizedError("invalid client id or key")
	}

	if bcrypt.CompareHashAndPassword([]byte(cred.ClientKey), []byte(req.ClientKey)) != nil {
		return nil, errors.NewUnauthorizedError("invalid client id or key")
	}

	return &proto_auth_v1.ValidateServiceCredentialsResponse{
		Valid:       true,
		ServiceId:   cred.ID,
		ServiceName: cred.Name,
	}, nil
}
