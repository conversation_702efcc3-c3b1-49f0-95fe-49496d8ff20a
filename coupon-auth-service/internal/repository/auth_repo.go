package repository

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
)

type authInfrastructure struct {
	db    *database.DB
	redis *redis.Client
}

type AuthRepository interface {
	CreateUserCredential(ctx context.Context, cred *model.UserCredential) error
	GetUserCredentialByEmail(ctx context.Context, email string) (*model.UserCredential, error)
	GetUserRoles(ctx context.Context, userID string) ([]string, error)
	RegisterService(ctx context.Context, service *model.ServiceCredential) error
	GetServiceByClientID(ctx context.Context, clientID string) (*model.ServiceCredential, error)
	RevokeToken(ctx context.Context, tokenID string) error
	IsTokenRevoked(ctx context.Context, tokenID string) (bool, error)
}

func NewAuthRepository(db *database.DB, redis *redis.Client) AuthRepository {
	return &authInfrastructure{db: db, redis: redis}
}

func (r *authInfrastructure) CreateUserCredential(ctx context.Context, cred *model.UserCredential) error {
	return r.db.WithContext(ctx).Create(cred).Error
}

func (r *authInfrastructure) GetUserCredentialByEmail(ctx context.Context, email string) (*model.UserCredential, error) {
	var cred model.UserCredential
	if err := r.db.WithContext(ctx).Where("email = ?", email).First(&cred).Error; err != nil {
		return nil, err
	}
	return &cred, nil
}

func (r *authInfrastructure) GetUserRoles(ctx context.Context, userID string) ([]string, error) {
	type role struct {
		Name string
	}
	var roles []role
	err := r.db.WithContext(ctx).
		Table("roles").
		Select("roles.name").
		Joins("JOIN user_roles ur ON ur.role_id = roles.id").
		Where("ur.user_id = ?", userID).
		Scan(&roles).Error
	if err != nil {
		return nil, err
	}
	names := make([]string, len(roles))
	for i, r := range roles {
		names[i] = r.Name
	}
	return names, nil
}

func (r *authInfrastructure) RegisterService(ctx context.Context, service *model.ServiceCredential) error {
	return r.db.WithContext(ctx).Create(service).Error
}

func (r *authInfrastructure) GetServiceByClientID(ctx context.Context, clientID string) (*model.ServiceCredential, error) {
	var cred model.ServiceCredential
	if err := r.db.WithContext(ctx).Where("client_id = ?", clientID).First(&cred).Error; err != nil {
		return nil, err
	}
	return &cred, nil
}

func (r *authInfrastructure) RevokeToken(ctx context.Context, tokenID string) error {
	key := fmt.Sprintf("revoked_token:%s", tokenID)
	return r.redis.Set(ctx, key, "revoked", 72*time.Hour)
}

func (r *authInfrastructure) IsTokenRevoked(ctx context.Context, tokenID string) (bool, error) {
	key := fmt.Sprintf("revoked_token:%s", tokenID)
	count, err := r.redis.Exists(ctx, key)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
