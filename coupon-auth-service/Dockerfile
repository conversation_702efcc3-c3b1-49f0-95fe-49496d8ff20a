# Stage 1: Builder - for compiling the application
FROM golang:1.24-alpine AS builder

WORKDIR /app

COPY go.mod go.sum ./

RUN go mod download

COPY . .

RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o auth-server ./cmd/server

# Stage 2: Final - for creating a minimal production image
FROM alpine:latest

# Add Certificate Authority certificates for making secure connections.
RUN apk --no-cache add ca-certificates

WORKDIR /app

COPY --from=builder /app/auth-server .

COPY config/config.yaml ./config/
COPY migrations ./migrations

# Expose the ports the service will listen on: HTTP, gRPC, and Metrics.
EXPOSE 8080
EXPOSE 50051
EXPOSE 2112

CMD ["./auth-server"]
