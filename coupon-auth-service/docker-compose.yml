version: "3.9"
services:
  postgres:
    image: postgres:16-alpine
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-auth_db}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data

  jaeger:
    image: jaegertracing/all-in-one:1.56
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "6831:6831/udp"

  auth-service:
    build: .
    depends_on:
      - postgres
      - redis
      - jaeger
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-auth_db}
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-secret}
      AUTH_SERVICE_API_KEY: ${AUTH_SERVICE_API_KEY:-change-me}
    ports:
      - "8080:8080"
      - "50051:50051"
      - "2112:2112"
volumes:
  postgres-data:
  redis-data:
