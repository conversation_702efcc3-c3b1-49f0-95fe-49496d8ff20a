syntax = "proto3";

package auth.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1";

service AuthService {
  rpc Login(LoginRequest) returns (LoginResponse);
  rpc ValidateUserToken(ValidateUserTokenRequest) returns (ValidateUserTokenResponse);
  rpc RevokeToken(RevokeTokenRequest) returns (RevokeTokenResponse);

  rpc CreateUserCredentials(CreateUserCredentialsRequest) returns (google.protobuf.Empty);

  rpc RegisterService(RegisterServiceRequest) returns (RegisterServiceResponse);
  rpc ValidateServiceCredentials(ValidateServiceCredentialsRequest) returns (ValidateServiceCredentialsResponse);

  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

message LoginRequest {
  common.v1.RequestMetadata metadata = 1;
  string email = 2;
  string password = 3;
}

message LoginResponse {
  common.v1.ResponseMetadata metadata = 1;
  string access_token = 2;
  string refresh_token = 3;
  google.protobuf.Timestamp expires_at = 4;
  common.v1.ServiceError error = 5;
}

message ValidateUserTokenRequest {
  common.v1.RequestMetadata metadata = 1;
  string token = 2;
  string required_role = 3;
  string resource = 4;
  string action = 5;
}

message ValidateUserTokenResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool valid = 2;
  string user_id = 3;
  string email = 4;
  repeated string roles = 5;
  repeated string permissions = 6;
  google.protobuf.Timestamp expires_at = 7;
  common.v1.ServiceError error = 8;
}

message RevokeTokenRequest {
  common.v1.RequestMetadata metadata = 1;
  string token = 2;
  string token_type = 3;
}

message RevokeTokenResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool revoked = 2;
  common.v1.ServiceError error = 3;
}

message CreateUserCredentialsRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  string email = 3;
  string password = 4;
}

message RegisterServiceRequest {
  common.v1.RequestMetadata metadata = 1;
  string service_name = 2;
  string service_version = 3;
  string description = 4;
  repeated string required_permissions = 5;
}

message RegisterServiceResponse {
  common.v1.ResponseMetadata metadata = 1;
  string service_id = 2;
  string client_id = 3;
  string client_key = 4;
  google.protobuf.Timestamp expires_at = 5;
  common.v1.ServiceError error = 6;
}

message ValidateServiceCredentialsRequest {
  common.v1.RequestMetadata metadata = 1;
  string client_id = 2;
  string client_key = 3;
  string service_name = 4;
}

message ValidateServiceCredentialsResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool valid = 2;
  string service_id = 3;
  string service_name = 4;
  repeated string permissions = 5;
  common.v1.ServiceError error = 6;
}
