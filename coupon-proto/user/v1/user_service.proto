syntax = "proto3";

package user.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "common/v1/pagination.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1";

service UserService {
  rpc GetUser(GetUserRequest) returns (GetUserResponse);
  rpc CreateUser(CreateUserRequest) returns (CreateUserResponse);
  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

message User {
  string id = 1;
  string email = 2;
  string name = 3;
  repeated string roles = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message GetUserRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
}

message GetUserResponse {
  common.v1.ResponseMetadata metadata = 1;
  User user = 2;
  common.v1.ServiceError error = 3;
}

message CreateUserRequest {
  common.v1.RequestMetadata metadata = 1;
  string name = 2;
  string email = 3;
  string password = 4;
}

message CreateUserResponse {
  common.v1.ResponseMetadata metadata = 1;
  User user = 2;
  common.v1.ServiceError error = 3;
}

message UserRegisteredEvent {
  string user_id = 1;
  string email = 2;
  string name = 3;
}
