# Database Separation Test Plan

## Overview
This document outlines how to test the database separation between auth-service and user-service to ensure proper microservice architecture.

## Test Scenarios

### 1. Database Schema Verification

**Auth Service Database (auth_db):**
```sql
-- Connect to auth_db and verify tables
\c auth_db
\dt

-- Expected tables:
-- - user_credentials
-- - service_credentials  
-- - refresh_tokens
-- - roles
-- - user_roles
```

**User Service Database (user_db):**
```sql
-- Connect to user_db and verify tables
\c user_db
\dt

-- Expected tables:
-- - users (only user profile data)
-- - NO roles or user_roles tables
```

### 2. User Registration Flow Test

**Test Steps:**
1. Start both auth-service and user-service
2. Call user registration API via API gateway
3. Verify data is stored in correct databases:
   - User profile in `user_db.users`
   - Credentials and role assignment in `auth_db.user_credentials` and `auth_db.user_roles`

**Expected Behavior:**
- User profile created in user-service database
- Credentials created in auth-service database
- Default "USER" role assigned in auth-service database
- Both services can retrieve user data through gRPC calls

### 3. Role Management Test

**Test Steps:**
1. Create a user through registration
2. Call auth-service to get user roles
3. Verify user-service can retrieve roles via gRPC call to auth-service

**Expected Behavior:**
- Auth-service owns all role data
- User-service gets roles through gRPC calls
- No direct database access between services

### 4. Email Update Test

**Test Steps:**
1. Update user email in user-service
2. Verify auth-service credentials are updated via gRPC call

**Expected Behavior:**
- Email updated in both services
- Data consistency maintained through service communication

## Manual Testing Commands

### Start Services
```bash
# Start auth-service
cd coupon-auth-service
docker-compose up -d postgres redis
go run cmd/server/main.go

# Start user-service (in another terminal)
cd coupon-user-service  
go run cmd/server/main.go

# Start API gateway (in another terminal)
cd coupon-api-gateway
go run cmd/server/main.go
```

### Test User Registration
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>", 
    "password": "password123"
  }'
```

### Verify Database Separation
```bash
# Check auth database
docker exec -it coupon-auth-service-postgres-1 psql -U coupon -d auth_db -c "SELECT * FROM user_credentials;"
docker exec -it coupon-auth-service-postgres-1 psql -U coupon -d auth_db -c "SELECT * FROM user_roles ur JOIN roles r ON ur.role_id = r.id;"

# Check user database (assuming separate postgres instance)
# This would need to be configured with separate database
```

## Success Criteria

✅ **Database Separation:**
- Auth-service only accesses auth_db
- User-service only accesses user_db
- No shared tables between services

✅ **Service Communication:**
- User-service calls auth-service for credential creation
- User-service calls auth-service for role information
- Auth-service updates work through gRPC calls

✅ **Data Consistency:**
- User registration creates data in both services
- Email updates propagate correctly
- Role assignments work properly

✅ **API Compatibility:**
- External APIs continue to work unchanged
- User registration flow works end-to-end
- Authentication and authorization work properly

## Notes

- This test plan assumes separate database instances for each service
- In the current setup, both services may share the same PostgreSQL instance but use different databases
- For full separation, consider using separate PostgreSQL instances in production
