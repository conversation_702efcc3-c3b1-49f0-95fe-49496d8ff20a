#!/bin/bash

# Service Credentials Setup Script
# This script helps register services with the auth-service and generate client credentials

set -e

AUTH_SERVICE_URL="${AUTH_SERVICE_URL:-http://localhost:8080}"
ADMIN_TOKEN="${ADMIN_TOKEN:-}"

if [ -z "$ADMIN_TOKEN" ]; then
    echo "Error: ADMIN_TOKEN environment variable is required"
    echo "Usage: ADMIN_TOKEN=<admin-jwt> ./setup_service_credentials.sh"
    exit 1
fi

echo "🔧 Setting up service credentials..."
echo "Auth Service URL: $AUTH_SERVICE_URL"

# Function to register a service
register_service() {
    local service_name=$1
    local service_version=$2
    local permissions=$3
    
    echo "📝 Registering $service_name..."
    
    response=$(curl -s -X POST "$AUTH_SERVICE_URL/admin/services" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"service_name\": \"$service_name\",
            \"service_version\": \"$service_version\",
            \"required_permissions\": $permissions
        }")
    
    if echo "$response" | grep -q "client_id"; then
        echo "✅ $service_name registered successfully"
        echo "$response" | jq '.'
        
        # Extract credentials for environment variables
        client_id=$(echo "$response" | jq -r '.client_id')
        client_key=$(echo "$response" | jq -r '.client_key')
        
        echo ""
        echo "🔑 Environment variables for $service_name:"
        echo "export ${service_name^^}_CLIENT_ID=\"$client_id\""
        echo "export ${service_name^^}_CLIENT_KEY=\"$client_key\""
        echo ""
    else
        echo "❌ Failed to register $service_name"
        echo "$response"
    fi
}

# Register services
echo "🚀 Starting service registration..."

# User Service
register_service "user-service" "1.0.0" '["user_management"]'

# Coupon Service (if exists)
register_service "coupon-service" "1.0.0" '["token_validation", "token_revocation"]'

# Order Service (if exists)  
register_service "order-service" "1.0.0" '["token_validation"]'

echo "✨ Service registration complete!"
echo ""
echo "📋 Next steps:"
echo "1. Add the environment variables to your service configurations"
echo "2. Update docker-compose.yml or Kubernetes manifests"
echo "3. Restart services to pick up new credentials"
echo ""
echo "🔍 To verify service registration:"
echo "curl -H \"Authorization: Bearer \$ADMIN_TOKEN\" $AUTH_SERVICE_URL/admin/services"
