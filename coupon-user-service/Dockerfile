# Stage 1: Builder - for compiling the application
FROM golang:1.24-alpine AS builder

WORKDIR /app

COPY go.mod go.sum ./

RUN go mod download

COPY . .

RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o user-server ./cmd/server

# Stage 2: Final - for creating a minimal production image
FROM alpine:latest


# Add Certificate Authority certificates for making secure connections.
RUN apk --no-cache add ca-certificates

WORKDIR /app

COPY --from=builder /app/user-server .

COPY config/config.yaml ./config/
COPY migrations ./migrations

EXPOSE 8080
EXPOSE 50051

CMD ["./user-server"]
