package model

import (
	"time"

	"github.com/google/uuid"
)

// User represents user profile data only
// Roles and authentication data are managed by auth-service
type User struct {
	ID        string    `gorm:"type:uuid;primary_key;"`
	Name      string    `gorm:"type:varchar(255);not null"`
	Email     string    `gorm:"type:varchar(255);not null;uniqueIndex"`
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (User) TableName() string { return "users" }

func NewUser(name, email string) *User {
	return &User{
		ID:    uuid.NewString(),
		Name:  name,
		Email: email,
	}
}
