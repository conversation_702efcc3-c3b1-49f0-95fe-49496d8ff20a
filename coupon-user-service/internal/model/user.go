package model

import (
	"time"

	"github.com/google/uuid"
)

type User struct {
	ID        string    `gorm:"type:uuid;primary_key;"`
	Name      string    `gorm:"type:varchar(255);not null"`
	Email     string    `gorm:"type:varchar(255);not null;uniqueIndex"`
	Roles     []*Role   `gorm:"many2many:user_roles;"`
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (User) TableName() string { return "users" }

type Role struct {
	ID   string `gorm:"type:uuid;primary_key;"`
	Name string `gorm:"type:varchar(100);not null;unique"`
}

func (Role) TableName() string { return "roles" }

func NewUser(name, email string, defaultRole *Role) *User {
	user := &User{
		ID:    uuid.NewString(),
		Name:  name,
		Email: email,
		Roles: []*Role{},
	}
	if defaultRole != nil {
		user.Roles = append(user.Roles, defaultRole)
	}
	return user
}

func (u *User) GetRoleNames() []string {
	names := make([]string, len(u.Roles))
	for i, r := range u.Roles {
		names[i] = r.Name
	}
	return names
}
