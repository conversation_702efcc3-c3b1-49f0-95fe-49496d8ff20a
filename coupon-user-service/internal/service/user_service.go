package service

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/repository"
)

type UserService interface {
	RegisterUser(ctx context.Context, name, email, password string) (*model.User, error)
	GetUser(ctx context.Context, userID string) (*model.User, error)
}

type userService struct {
	repo          repository.UserRepository
	authClient    *clients.AuthClient
	kafkaProducer *kafka.Producer
	logger        *logging.Logger
	kafkaTopics   *config.KafkaTopicsConfig
}

func NewUserService(repo repository.UserRepository, authClient *clients.AuthClient, kafkaProducer *kafka.Producer, logger *logging.Logger, topics *config.KafkaTopicsConfig) UserService {
	return &userService{
		repo:          repo,
		authClient:    authClient,
		kafkaProducer: kafkaProducer,
		logger:        logger,
		kafkaTopics:   topics,
	}
}

func (s *userService) RegisterUser(ctx context.Context, name, email, password string) (*model.User, error) {
	log := s.logger.WithContext(ctx)
	log.Infof("Attempting to register user with email: %s", email)

	_, err := s.repo.GetByEmail(ctx, email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, app_errors.NewInternalError(fmt.Sprintf("db error checking for existing user: %v", err))
	}
	if err == nil {
		return nil, app_errors.NewConflictError(fmt.Sprintf("user with email %s already exists", email))
	}

	defaultRole, err := s.repo.GetRoleByName(ctx, "USER")
	if err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("default role 'USER' not found: %v", err))
	}

	user := model.NewUser(name, email, defaultRole)
	if err := s.repo.Create(ctx, user); err != nil {
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to create user profile: %v", err))
	}
	log.Infof("User profile created for user_id: %s", user.ID)

	if err := s.authClient.CreateUserCredentials(ctx, user.ID, user.Email, password); err != nil {
		log.Errorf("CRITICAL: Failed to create credentials for user %s after profile creation: %v", user.ID, err)
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to finalize user registration: %v", err))
	}
	log.Infof("User credentials created via Auth service for user_id: %s", user.ID)

	eventPayload := &proto_user_v1.UserRegisteredEvent{
		UserId: user.ID,
		Email:  user.Email,
		Name:   user.Name,
	}
	kafkaMsg := &kafka.Message{
		Key:   user.ID,
		Value: eventPayload,
	}
	if err := s.kafkaProducer.SendMessage(ctx, s.kafkaTopics.UserRegistered, kafkaMsg); err != nil {
		log.Errorf("Failed to publish UserRegisteredEvent for user %s: %v", user.ID, err)
	}

	return user, nil
}

func (s *userService) GetUser(ctx context.Context, userID string) (*model.User, error) {
	user, err := s.repo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, app_errors.NewNotFoundError(fmt.Sprintf("user with id %s not found", userID))
		}
		return nil, app_errors.NewInternalError(fmt.Sprintf("failed to get user: %v", err))
	}
	return user, nil
}
