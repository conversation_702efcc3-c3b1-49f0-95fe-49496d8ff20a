package grpc_handler

import (
	"context"

	app_errors "gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"

	commonv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	userv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"

	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/service"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type UserServer struct {
	userv1.UnimplementedUserServiceServer
	svc        service.UserService
	authClient *clients.AuthClient
}

func NewUserServer(svc service.UserService, authClient *clients.AuthClient) *UserServer {
	return &UserServer{svc: svc, authClient: authClient}
}

func (s *UserServer) GetUser(ctx context.Context, req *userv1.GetUserRequest) (*userv1.GetUserResponse, error) {
	user, err := s.svc.GetUser(ctx, req.UserId)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	// Get user roles from auth-service
	roles, err := s.authClient.GetUserRoles(ctx, user.ID)
	if err != nil {
		// Log error but don't fail the request - roles are optional for display
		roles = []string{}
	}

	return &userv1.GetUserResponse{
		User: &userv1.User{
			Id:        user.ID,
			Email:     user.Email,
			Name:      user.Name,
			Roles:     roles,
			CreatedAt: timestamppb.New(user.CreatedAt),
			UpdatedAt: timestamppb.New(user.UpdatedAt),
		},
	}, nil
}

func (s *UserServer) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserResponse, error) {
	user, err := s.svc.RegisterUser(ctx, req.Name, req.Email, req.Password)
	if err != nil {
		return nil, app_errors.ToGRPCError(err)
	}

	// Get user roles from auth-service (should have the default role assigned)
	roles, err := s.authClient.GetUserRoles(ctx, user.ID)
	if err != nil {
		// Log error but don't fail the request - roles are optional for display
		roles = []string{}
	}

	return &userv1.CreateUserResponse{
		User: &userv1.User{
			Id:        user.ID,
			Email:     user.Email,
			Name:      user.Name,
			Roles:     roles,
			CreatedAt: timestamppb.New(user.CreatedAt),
			UpdatedAt: timestamppb.New(user.UpdatedAt),
		},
	}, nil
}

func (s *UserServer) HealthCheck(ctx context.Context, req *commonv1.HealthCheckRequest) (*commonv1.HealthCheckResponse, error) {
	return &commonv1.HealthCheckResponse{Status: commonv1.HealthCheckResponse_SERVING_STATUS_SERVING}, nil
}
