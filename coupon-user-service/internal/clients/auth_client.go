package clients

import (
	"context"
	"fmt"
	"time"

	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"google.golang.org/grpc/metadata"
)

type AuthClient struct {
	client    proto_auth_v1.AuthServiceClient
	conn      *shared_grpc.Client
	clientID  string
	clientKey string
}

func NewAuthClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*AuthClient, error) {
	client, err := shared_grpc.NewClient(target, cfg, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for auth service: %w", err)
	}

	return &AuthClient{
		client:    proto_auth_v1.NewAuthServiceClient(client.GetConnection()),
		conn:      client,
		clientID:  clientID,
		clientKey: clientKey,
	}, nil
}

func (c *AuthClient) Close() {
	c.conn.Close()
}

func (c *AuthClient) CreateUserCredentials(ctx context.Context, userID, email, password string) error {
	req := &proto_auth_v1.CreateUserCredentialsRequest{
		UserId:   userID,
		Email:    email,
		Password: password,
	}

	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	_, err := c.client.CreateUserCredentials(ctx, req)
	return err
}

func (c *AuthClient) CreateUserCredentialsWithRole(ctx context.Context, userID, email, password, roleName string) error {
	req := &proto_auth_v1.CreateUserCredentialsWithRoleRequest{
		UserId:   userID,
		Email:    email,
		Password: password,
		RoleName: roleName,
	}

	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	_, err := c.client.CreateUserCredentialsWithRole(ctx, req)
	return err
}

func (c *AuthClient) GetUserRoles(ctx context.Context, userID string) ([]string, error) {
	req := &proto_auth_v1.GetUserRolesRequest{
		UserId: userID,
	}

	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.GetUserRoles(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.Roles, nil
}
