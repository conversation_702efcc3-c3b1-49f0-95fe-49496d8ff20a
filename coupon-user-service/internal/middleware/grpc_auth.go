package middleware

import (
	"context"

	grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	"google.golang.org/grpc"
)

// AuthInterceptor is a custom gRPC interceptor that selectively applies JWT authentication.
func AuthInterceptor(jwtAuthFunc grpc_auth.AuthFunc) grpc.UnaryServerInterceptor {
	// Create the standard JWT auth interceptor once.
	jwtInterceptor := grpc_auth.UnaryServerInterceptor(jwtAuthFunc)

	// Return a new interceptor that acts as a router.
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		// Define a list of public methods that do not require authentication.
		publicMethods := map[string]bool{
			"/user.v1.UserService/CreateUser": true,
			// The HealthCheck is also often public.
			"/user.v1.UserService/HealthCheck": true,
		}

		// Check if the current RPC method is in our public list.
		if publicMethods[info.FullMethod] {
			// If it's a public method, skip the JWT interceptor and call the handler directly.
			return handler(ctx, req)
		}

		// If it's not a public method, apply the JWT authentication interceptor.
		return jwtInterceptor(ctx, req, info, handler)
	}
}
