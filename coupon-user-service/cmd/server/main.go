package main

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	grpc_ctxtags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
	grpc_opentracing "github.com/grpc-ecosystem/go-grpc-middleware/tracing/opentracing"
	"github.com/labstack/echo/v4"
	userv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/kafka"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/clients"
	grpc_handler "gitlab.zalopay.vn/phunn4/coupon-user-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/middleware"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-user-service/internal/service"
	"google.golang.org/grpc"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("config error: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, "json")
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	tracer, err := tracing.New(cfg.Service.Name, cfg.Jaeger.Host, cfg.Jaeger.Port)
	if err != nil {
		logger.Fatalf("tracer error: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)
	jwtManager := auth.NewJWTManager(&cfg.Auth)

	// --- Infrastructure ---
	db, err := database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		logger.Fatalf("db error: %v", err)
	}
	redisClient := redis.NewClient(&cfg.Redis, logger, appMetrics)
	kafkaProducer := kafka.NewProducer(&cfg.Kafka, logger)
	authClient, err := clients.NewAuthClient(cfg.DownstreamServices.AuthServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.ClientID, cfg.Service.ClientKey)
	if err != nil {
		logger.Fatalf("auth client error: %v", err)
	}

	// --- DI ---
	repo := repository.NewUserRepository(db, redisClient, logger)
	svc := service.NewUserService(repo, authClient, kafkaProducer, logger, &cfg.Kafka.Topics)

	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)
	healthChecker.AddCheck("redis", redisClient.Health)

	// --- Run ---
	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		// **FIXED**: Pass the entire jwtManager struct, not just the function.
		startGRPCServer(ctx, cfg, logger, svc, jwtManager)
	}()
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, healthChecker, appMetrics, logger)
	}()

	wg.Wait()
	tracer.Close()
	db.Close()
	redisClient.Close()
	kafkaProducer.Close()
	authClient.Close()
	logger.Info("Shutdown complete")
}

// **FIXED**: The function signature now correctly accepts the *auth.JWTManager.
func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, svc service.UserService, jwtManager *auth.JWTManager) {
	addr := fmt.Sprintf(":%d", cfg.GRPC.Port)
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		logger.Fatalf("Failed to listen on gRPC port %s: %v", addr, err)
	}

	// Create the custom auth interceptor by passing the AuthFunc from the manager.
	authInterceptor := middleware.AuthInterceptor(jwtManager.AuthFunc)

	grpcServer := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			grpc_ctxtags.UnaryServerInterceptor(),
			grpc_opentracing.UnaryServerInterceptor(),
			grpc_recovery.UnaryServerInterceptor(),
			authInterceptor,
		),
	)

	grpcHandler := grpc_handler.NewUserServer(svc)
	userv1.RegisterUserServiceServer(grpcServer, grpcHandler)

	logger.Infof("gRPC server listening at %s", addr)
	if err := grpcServer.Serve(listener); err != nil {
		logger.Errorf("gRPC server failed: %v", err)
	}
}

// startHTTPServer remains unchanged.
func startHTTPServer(ctx context.Context, cfg *config.Config, healthChecker *health.HealthChecker, metrics *metrics.Metrics, logger *logging.Logger) {
	e := echo.New()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET(cfg.Metrics.Path, echo.WrapHandler(metrics.Handler()))

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("Starting operational HTTP server on %s", addr)
	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP server failed: %v", err)
		}
	}()

	<-ctx.Done()
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("HTTP server shutdown failed: %v", err)
	}
}
